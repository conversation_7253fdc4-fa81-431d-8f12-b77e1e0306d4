#!/usr/bin/env python3
"""
Output Validation Script for USB PD Parser
Validates the JSON/JSONL output files for correctness and completeness
"""

import json
import jsonlines
from pathlib import Path
from typing import Dict, List, Any, Tuple, Optional
import yaml
from dataclasses import dataclass
import logging
import re
from datetime import datetime

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

@dataclass
class ValidationResult:
    """Results of validation process"""
    is_valid: bool
    errors: List[str]
    warnings: List[str]
    statistics: Dict[str, Any]
    score: float

class OutputValidator:
    """Validates parsed USB PD specification outputs"""
    
    def __init__(self, config_file: str = "config.yaml"):
        """Initialize validator with configuration"""
        self.config = self._load_config(config_file)
        self.validation_rules = self.config.get('validation', {})
        
    def _load_config(self, config_file: str) -> Dict:
        """Load configuration from YAML file"""
        try:
            with open(config_file, 'r') as f:
                return yaml.safe_load(f)
        except FileNotFoundError:
            logger.warning(f"Config file {config_file} not found, using defaults")
            return self._get_default_config()
        except yaml.YAMLError as e:
            logger.error(f"Error loading config: {e}")
            return self._get_default_config()
    
    def _get_default_config(self) -> Dict:
        """Get default validation configuration"""
        return {
            'validation': {
                'required_sections': ['introduction', 'overview', 'specification'],
                'expected_voltage_ranges': {'min': 3.0, 'max': 48.0},
                'expected_current_ranges': {'min': 0.1, 'max': 10.0},
                'expected_power_ranges': {'min': 1.0, 'max': 240.0}
            }
        }
    
    def validate_json_structure(self, data: Dict) -> Tuple[List[str], List[str]]:
        """Validate JSON structure and required fields"""
        errors = []
        warnings = []
        
        # Check required top-level fields
        required_fields = [
            'version', 'title', 'sections', 'tables', 
            'voltage_profiles', 'current_profiles', 'metadata'
        ]
        
        for field in required_fields:
            if field not in data:
                errors.append(f"Missing required field: {field}")
            elif not data[field] and field in ['sections', 'metadata']:
                errors.append(f"Required field '{field}' is empty")
        
        # Check data types
        type_checks = {
            'version': str,
            'title': str,
            'sections': list,
            'tables': list,
            'voltage_profiles': list,
            'current_profiles': list,
            'metadata': dict
        }
        
        for field, expected_type in type_checks.items():
            if field in data and not isinstance(data[field], expected_type):
                errors.append(f"Field '{field}' should be {expected_type.__name__}, got {type(data[field]).__name__}")
        
        # Check metadata completeness
        if 'metadata' in data:
            metadata_fields = ['extraction_date', 'file_name', 'total_pages']
            for field in metadata_fields:
                if field not in data['metadata']:
                    warnings.append(f"Missing metadata field: {field}")
        
        return errors, warnings
    
    def validate_sections(self, sections: List[Dict]) -> Tuple[List[str], List[str]]:
        """Validate document sections"""
        errors = []
        warnings = []
        
        if not sections:
            errors.append("No sections found in document")
            return errors, warnings
        
        # Check section structure
        required_section_fields = ['number', 'title', 'content']
        for i, section in enumerate(sections):
            for field in required_section_fields:
                if field not in section:
                    errors.append(f"Section {i}: Missing field '{field}'")
                elif not section[field] and field != 'content':
                    warnings.append(f"Section {i}: Empty field '{field}'")
        
        # Check for expected sections
        section_titles = [s.get('title', '').lower() for s in sections]
        required_sections = self.validation_rules.get('required_sections', [])
        
        for req_section in required_sections:
            found = any(req_section.lower() in title for title in section_titles)
            if not found:
                warnings.append(f"Expected section not found: {req_section}")
        
        # Check section numbering
        section_numbers = [s.get('number') for s in sections if s.get('number')]
        if len(set(section_numbers)) != len(section_numbers):
            warnings.append("Duplicate section numbers found")
        
        return errors, warnings
    
    def validate_tables(self, tables: List[Dict]) -> Tuple[List[str], List[str]]:
        """Validate extracted tables"""
        errors = []
        warnings = []
        
        for i, table in enumerate(tables):
            # Check table structure
            required_fields = ['number', 'title']
            for field in required_fields:
                if field not in table:
                    errors.append(f"Table {i}: Missing field '{field}'")
            
            # Check table content
            if 'headers' in table and 'rows' in table:
                if table['headers'] and table['rows']:
                    header_count = len(table['headers'])
                    for j, row in enumerate(table['rows']):
                        if len(row) != header_count:
                            warnings.append(f"Table {i}, row {j}: Column count mismatch")
                elif not table['headers'] and not table['rows']:
                    warnings.append(f"Table {i}: No data extracted")
        
        return errors, warnings
    
    def validate_voltage_profiles(self, profiles: List[Dict]) -> Tuple[List[str], List[str]]:
        """Validate voltage specifications"""
        errors = []
        warnings = []
        
        voltage_range = self.validation_rules.get('expected_voltage_ranges', {})
        min_voltage = voltage_range.get('min', 0)
        max_voltage = voltage_range.get('max', 100)
        
        for i, profile in enumerate(profiles):
            if 'voltage' not in profile:
                errors.append(f"Voltage profile {i}: Missing voltage value")
                continue
            
            voltage = profile['voltage']
            if not isinstance(voltage, (int, float)):
                errors.append(f"Voltage profile {i}: Invalid voltage type")
                continue
            
            if voltage < min_voltage or voltage > max_voltage:
                warnings.append(f"Voltage profile {i}: Voltage {voltage}V outside expected range ({min_voltage}-{max_voltage}V)")
        
        return errors, warnings
    
    def validate_current_profiles(self, profiles: List[Dict]) -> Tuple[List[str], List[str]]:
        """Validate current specifications"""
        errors = []
        warnings = []
        
        current_range = self.validation_rules.get('expected_current_ranges', {})
        min_current = current_range.get('min', 0)
        max_current = current_range.get('max', 20)
        
        for i, profile in enumerate(profiles):
            if 'current' not in profile:
                errors.append(f"Current profile {i}: Missing current value")
                continue
            
            current = profile['current']
            if not isinstance(current, (int, float)):
                errors.append(f"Current profile {i}: Invalid current type")
                continue
            
            if current < min_current or current > max_current:
                warnings.append(f"Current profile {i}: Current {current}A outside expected range ({min_current}-{max_current}A)")
        
        return errors, warnings
    
    def validate_message_types(self, messages: List[Dict]) -> Tuple[List[str], List[str]]:
        """Validate message types"""
        errors = []
        warnings = []
        
        expected_messages = [
            'REQUEST', 'RESPONSE', 'DATA_ROLE_SWAP', 'POWER_ROLE_SWAP',
            'VCONN_SWAP', 'GOTO_MIN', 'ACCEPT', 'REJECT', 'PS_RDY', 'GET_SOURCE_CAP'
        ]
        
        found_messages = [msg.get('type', '') for msg in messages]
        
        for expected in expected_messages:
            if expected not in found_messages:
                warnings.append(f"Expected message type not found: {expected}")
        
        # Check message structure
        for i, message in enumerate(messages):
            if 'type' not in message:
                errors.append(f"Message {i}: Missing type field")
            if 'description' not in message:
                warnings.append(f"Message {i}: Missing description field")
        
        return errors, warnings
    
    def calculate_completeness_score(self, data: Dict) -> float:
        """Calculate a completeness score (0-100)"""
        score = 0
        max_score = 100
        
        # Basic structure (20 points)
        required_fields = ['version', 'title', 'sections', 'tables', 'metadata']
        present_fields = sum(1 for field in required_fields if field in data and data[field])
        score += (present_fields / len(required_fields)) * 20
        
        # Sections completeness (30 points)
        if 'sections' in data and data['sections']:
            sections = data['sections']
            complete_sections = sum(1 for s in sections if all(f in s for f in ['number', 'title', 'content']))
            score += (complete_sections / len(sections)) * 30
        
        # Tables completeness (20 points)
        if 'tables' in data and data['tables']:
            tables = data['tables']
            complete_tables = sum(1 for t in tables if all(f in t for f in ['number', 'title', 'headers', 'rows']))
            score += (complete_tables / len(tables)) * 20
        
        # Voltage profiles (15 points)
        if 'voltage_profiles' in data and data['voltage_profiles']:
            profiles = data['voltage_profiles']
            valid_profiles = sum(1 for p in profiles if 'voltage' in p and isinstance(p['voltage'], (int, float)))
            score += (valid_profiles / len(profiles)) * 15
        
        # Current profiles (15 points)
        if 'current_profiles' in data and data['current_profiles']:
            profiles = data['current_profiles']
            valid_profiles = sum(1 for p in profiles if 'current' in p and isinstance(p['current'], (int, float)))
            score += (valid_profiles / len(profiles)) * 15
        
        return min(score, max_score)
    
    def validate_data_consistency(self, data: Dict) -> Tuple[List[str], List[str]]:
        """Validate data consistency across different sections"""
        errors = []
        warnings = []
        
        # Check if voltage and current profiles are consistent with tables
        if 'tables' in data and 'voltage_profiles' in data:
            table_voltages = []
            for table in data['tables']:
                if 'rows' in table:
                    for row in table['rows']:
                        for cell in row:
                            # Extract voltage values from table cells
                            voltage_matches = re.findall(r'(\d+(?:\.\d+)?)\s*V', str(cell))
                            table_voltages.extend([float(v) for v in voltage_matches])
            
            profile_voltages = [p.get('voltage') for p in data['voltage_profiles'] if 'voltage' in p]
            
            # Check if profile voltages are found in tables
            for voltage in profile_voltages:
                if voltage and not any(abs(voltage - tv) < 0.1 for tv in table_voltages):
                    warnings.append(f"Voltage {voltage}V in profiles not found in tables")
        
        return errors, warnings
    
    def validate_file(self, file_path: str) -> ValidationResult:
        """Validate a single JSON/JSONL file"""
        errors = []
        warnings = []
        statistics = {}
        
        try:
            file_path = Path(file_path)
            
            if not file_path.exists():
                errors.append(f"File not found: {file_path}")
                return ValidationResult(False, errors, warnings, statistics, 0.0)
            
            # Load data based on file extension
            if file_path.suffix == '.jsonl':
                data_list = []
                with jsonlines.open(file_path) as reader:
                    for obj in reader:
                        data_list.append(obj)
                
                # For JSONL, validate each object
                for i, data in enumerate(data_list):
                    obj_errors, obj_warnings = self._validate_single_object(data)
                    errors.extend([f"Object {i}: {e}" for e in obj_errors])
                    warnings.extend([f"Object {i}: {w}" for w in obj_warnings])
                
                statistics['total_objects'] = len(data_list)
                
                # Calculate average score
                scores = [self.calculate_completeness_score(obj) for obj in data_list]
                avg_score = sum(scores) / len(scores) if scores else 0
                
            else:  # JSON file
                with open(file_path, 'r') as f:
                    data = json.load(f)
                
                errors, warnings = self._validate_single_object(data)
                statistics['total_objects'] = 1
                avg_score = self.calculate_completeness_score(data)
            
            # Add file statistics
            statistics.update({
                'file_size': file_path.stat().st_size,
                'file_name': file_path.name,
                'validation_date': datetime.now().isoformat()
            })
            
            is_valid = len(errors) == 0
            
            return ValidationResult(is_valid, errors, warnings, statistics, avg_score)
            
        except json.JSONDecodeError as e:
            errors.append(f"Invalid JSON format: {e}")
        except Exception as e:
            errors.append(f"Unexpected error: {e}")
        
        return ValidationResult(False, errors, warnings, statistics, 0.0)
    
    def _validate_single_object(self, data: Dict) -> Tuple[List[str], List[str]]:
        """Validate a single data object"""
        all_errors = []
        all_warnings = []
        
        # Structure validation
        errors, warnings = self.validate_json_structure(data)
        all_errors.extend(errors)
        all_warnings.extend(warnings)
        
        # Sections validation
        if 'sections' in data:
            errors, warnings = self.validate_sections(data['sections'])
            all_errors.extend(errors)
            all_warnings.extend(warnings)
        
        # Tables validation
        if 'tables' in data:
            errors, warnings = self.validate_tables(data['tables'])
            all_errors.extend(errors)
            all_warnings.extend(warnings)
        
        # Voltage profiles validation
        if 'voltage_profiles' in data:
            errors, warnings = self.validate_voltage_profiles(data['voltage_profiles'])
            all_errors.extend(errors)
            all_warnings.extend(warnings)
        
        # Current profiles validation
        if 'current_profiles' in data:
            errors, warnings = self.validate_current_profiles(data['current_profiles'])
            all_errors.extend(errors)
            all_warnings.extend(warnings)
        
        # Message types validation (if present)
        if 'message_types' in data:
            errors, warnings = self.validate_message_types(data['message_types'])
            all_errors.extend(errors)
            all_warnings.extend(warnings)
        
        # Data consistency validation
        errors, warnings = self.validate_data_consistency(data)
        all_errors.extend(errors)
        all_warnings.extend(warnings)
        
        return all_errors, all_warnings
    
    def validate_directory(self, directory_path: str, pattern: str = "*.json*") -> Dict[str, ValidationResult]:
        """Validate all JSON/JSONL files in a directory"""
        results = {}
        directory = Path(directory_path)
        
        if not directory.exists():
            logger.error(f"Directory not found: {directory_path}")
            return results
        
        files = list(directory.glob(pattern))
        
        if not files:
            logger.warning(f"No files matching pattern '{pattern}' found in {directory_path}")
            return results
        
        logger.info(f"Validating {len(files)} files in {directory_path}")
        
        for file_path in files:
            logger.info(f"Validating: {file_path.name}")
            result = self.validate_file(str(file_path))
            results[str(file_path)] = result
            
            if result.is_valid:
                logger.info(f"✓ {file_path.name}: Valid (Score: {result.score:.1f})")
            else:
                logger.error(f"✗ {file_path.name}: Invalid ({len(result.errors)} errors)")
        
        return results
    
    def generate_report(self, results: Dict[str, ValidationResult], output_file: Optional[str] = None) -> str:
        """Generate a validation report"""
        report = []
        report.append("USB PD Parser Output Validation Report")
        report.append("=" * 50)
        report.append(f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report.append("")
        
        total_files = len(results)
        valid_files = sum(1 for r in results.values() if r.is_valid)
        invalid_files = total_files - valid_files
        
        report.append(f"Summary:")
        report.append(f"  Total files validated: {total_files}")
        report.append(f"  Valid files: {valid_files}")
        report.append(f"  Invalid files: {invalid_files}")
        report.append("")
        
        if results:
            avg_score = sum(r.score for r in results.values()) / len(results)
            report.append(f"  Average completeness score: {avg_score:.1f}/100")
            report.append("")
        
        # Detailed results
        for file_path, result in results.items():
            report.append(f"File: {Path(file_path).name}")
            report.append(f"  Status: {'VALID' if result.is_valid else 'INVALID'}")
            report.append(f"  Score: {result.score:.1f}/100")
            
            if result.errors:
                report.append(f"  Errors ({len(result.errors)}):")
                for error in result.errors:
                    report.append(f"    - {error}")
            
            if result.warnings:
                report.append(f"  Warnings ({len(result.warnings)}):")
                for warning in result.warnings:
                    report.append(f"    - {warning}")
            
            if result.statistics:
                report.append(f"  Statistics:")
                for key, value in result.statistics.items():
                    report.append(f"    {key}: {value}")
            
            report.append("")
        
        report_text = "\n".join(report)
        
        if output_file:
            with open(output_file, 'w') as f:
                f.write(report_text)
            logger.info(f"Report saved to: {output_file}")
        
        return report_text


def main():
    """Main validation function"""
    import argparse
    
    parser = argparse.ArgumentParser(description="Validate USB PD Parser outputs")
    parser.add_argument("input", help="Input file or directory path")
    parser.add_argument("--config", default="config.yaml", help="Configuration file path")
    parser.add_argument("--output", help="Output report file path")
    parser.add_argument("--pattern", default="*.json*", help="File pattern for directory validation")
    parser.add_argument("--verbose", action="store_true", help="Enable verbose logging")
    
    args = parser.parse_args()
    
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    
    validator = OutputValidator(args.config)
    
    input_path = Path(args.input)
    
    if input_path.is_file():
        # Validate single file
        result = validator.validate_file(str(input_path))
        results = {str(input_path): result}
    elif input_path.is_dir():
        # Validate directory
        results = validator.validate_directory(str(input_path), args.pattern)
    else:
        logger.error(f"Invalid input path: {input_path}")
        return
    
    # Generate and display report
    report = validator.generate_report(results, args.output)
    print(report)
    
    # Exit with error code if any validation failed
    if any(not r.is_valid for r in results.values()):
        exit(1)


if __name__ == "__main__":
    main()