# USB PD Parser - File Guide

## 📋 Essential Files (REQUIRED)

### Core Application Files
- **`usb_pd_parser.py`** - Main parser engine
  - Extracts Table of Contents from PDF files
  - Handles different document types
  - Core functionality

- **`demo.py`** - Interactive interface
  - Main entry point for users
  - PDF selection menu
  - Analysis and reporting features

- **`config.yaml`** - Configuration file
  - Parser settings and patterns
  - Output format options
  - Validation rules

- **`requirements.txt`** - Dependencies
  - Lists required Python packages
  - Cleaned up to include only essentials

### Essential Directories
- **`pdfs/`** - Input PDF files
  - Place your PDF files here
  - Parser will scan this directory

- **`output/`** - Generated results
  - JSONL output files
  - Analysis reports
  - Auto-created by parser

## 🔧 Optional Files (Can be deleted)

- **`test_usb.py`** - Unit tests
  - Only needed for development
  - Can be deleted if you just want to use the parser

- **`validate.py`** - Advanced validation
  - Optional validation features
  - Can be deleted for basic usage

- **`cleanup.py`** - This cleanup script
  - Helps remove optional files
  - Can be deleted after use

## 🗑️ Auto-Generated (Can be deleted)

- **`__pycache__/`** - Python cache
  - Auto-generated by Python
  - Safe to delete (will be recreated)

## 🚀 Minimal Setup

For the most minimal setup, you only need:
```
UMA_Parser/
├── usb_pd_parser.py
├── demo.py
├── config.yaml
├── requirements.txt
├── pdfs/
│   └── your_file.pdf
└── output/ (created automatically)
```

## 📦 Installation

1. Install dependencies:
   ```bash
   pip install -r requirements.txt
   ```

2. Run the parser:
   ```bash
   python demo.py
   ```

## 🧹 Cleanup

To remove optional files and keep only essentials:
```bash
python cleanup.py
```

This will ask for confirmation before deleting anything.
