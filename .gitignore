# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class

# C extensions
*.so

# Distribution / packaging
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
pip-wheel-metadata/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
#  Usually these files are written by a python script from a template
#  before PyInstaller builds the exe, so as to inject date/other infos into it.
*.manifest
*.spec

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.py,cover
.hypothesis/
.pytest_cache/

# Translations
*.mo
*.pot

# Django stuff:
*.log
local_settings.py
db.sqlite3
db.sqlite3-journal

# Flask stuff:
instance/
.webassets-cache

# Scrapy stuff:
.scrapy

# Sphinx documentation
docs/_build/

# PyBuilder
target/

# Jupyter Notebook
.ipynb_checkpoints

# IPython
profile_default/
ipython_config.py

# pyenv
.python-version

# pipenv
#   According to pypa/pipenv#598, it is recommended to include Pipfile.lock in version control.
#   However, in case of collaboration, if having platform-specific dependencies or dependencies
#   having no cross-platform support, pipenv may install dependencies that don't work, or not
#   install all needed dependencies.
#Pipfile.lock

# PEP 582; used by e.g. github.com/David-OConnor/pyflow
__pypackages__/

# Celery stuff
celerybeat-schedule
celerybeat.pid

# SageMath parsed files
*.sage.py

# Environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# Spyder project settings
.spyderproject
.spyproject

# Rope project settings
.ropeproject

# mkdocs documentation
/site

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Pyre type checker
.pyre/

# Project specific
# PDF files (large files, should be stored elsewhere or downloaded)
pdfs/*.pdf
!pdfs/sample_*.pdf  # Keep sample files

# Output directories
output/
logs/
temp/
cache/

# IDE and editor files
.vscode/
.idea/
*.swp
*.swo
*~

# macOS
.DS_Store
.AppleDouble
.LSOverride

# Windows
Thumbs.db
ehthumbs.db
Desktop.ini
$RECYCLE.BIN/

# Linux
*~

# Backup files
*.bak
*.backup
*.orig

# Temporary files
*.tmp
*.temp

# Data files (may contain sensitive information)
*.csv
*.xlsx
*.json
*.jsonl
!examples/*.json
!examples/*.jsonl

# Model files and caches
models/
*.pkl
*.pickle
*.joblib

# Large datasets
datasets/
data/

# Configuration overrides (keep template configs)
config_local.yaml
config_production.yaml
.config/

# SSL certificates and keys
*.pem
*.key
*.crt
*.csr

# Database files
*.db
*.sqlite
*.sqlite3

# Profiling data
*.prof

# Memory dumps
*.dmp

# Core dumps
core.*

# PyCharm
.idea/

# Visual Studio Code
.vscode/
*.code-workspace

# Sublime Text
*.sublime-project
*.sublime-workspace

# Vim
*.vim

# Emacs
*~
\#*\#
/.emacs.desktop
/.emacs.desktop.lock
*.elc
auto-save-list
tramp
.\#*

# Project artifacts
artifacts/
reports/
exports/
results/

# Testing artifacts
.coverage.*
htmlcov/
.pytest_cache/
test-results/
test-reports/

# Documentation builds
docs/build/
docs/dist/

# Miscellaneous
*.log.*
*.out
*.err