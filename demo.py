#!/usr/bin/env python3
"""
USB PD Parser Demo and Validation Script

This script demonstrates the USB PD parser capabilities and provides
comprehensive validation and analysis of parsed results.

Usage:
    python demo.py
    python demo.py --analysis-only output/usb_pd_spec.jsonl
    python demo.py --compare file1.jsonl file2.jsonl
"""

import json
import argparse
import os
import sys
from pathlib import Path
from typing import List, Dict, Any
import logging
from collections import Counter, defaultdict
import difflib

# Import our parser
from usb_pd_parser import USBPDParser, TOCEntry, ParseConfig

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class TOCAnalyzer:
    """Analyzer for TOC data and validation"""
    
    def __init__(self):
        pass
    
    def load_jsonl(self, file_path: str) -> List[Dict]:
        """Load TOC entries from JSONL file"""
        entries = []
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                for line_num, line in enumerate(f, 1):
                    line = line.strip()
                    if line:
                        try:
                            entry = json.loads(line)
                            entries.append(entry)
                        except json.JSONDecodeError as e:
                            logger.warning(f"Invalid JSON on line {line_num}: {e}")
                            
        except FileNotFoundError:
            logger.error(f"File not found: {file_path}")
            raise
        except Exception as e:
            logger.error(f"Error loading {file_path}: {e}")
            raise
            
        logger.info(f"Loaded {len(entries)} entries from {file_path}")
        return entries
    
    def analyze_structure(self, entries: List[Dict]) -> Dict[str, Any]:
        """Perform comprehensive structure analysis"""
        analysis = {
            'total_entries': len(entries),
            'hierarchy': {},
            'page_stats': {},
            'title_analysis': {},
            'tag_analysis': {},
            'quality_metrics': {}
        }
        
        if not entries:
            return analysis
        
        # Hierarchy analysis
        levels = [entry.get('level', 0) for entry in entries]
        analysis['hierarchy'] = {
            'max_depth': max(levels),
            'min_depth': min(levels),
            'level_distribution': dict(Counter(levels))
        }
        
        # Page statistics
        pages = [entry.get('page', 0) for entry in entries if entry.get('page')]
        if pages:
            analysis['page_stats'] = {
                'first_page': min(pages),
                'last_page': max(pages),
                'total_pages_covered': max(pages) - min(pages) + 1,
                'avg_pages_per_section': round((max(pages) - min(pages) + 1) / len(entries), 2)
            }
        
        # Title analysis
        titles = [entry.get('title', '') for entry in entries]
        title_lengths = [len(title) for title in titles]
        analysis['title_analysis'] = {
            'avg_title_length': round(sum(title_lengths) / len(title_lengths), 2) if title_lengths else 0,
            'longest_title': max(titles, key=len) if titles else '',
            'shortest_title': min(titles, key=len) if titles else ''
        }
        
        # Tag analysis
        all_tags = []
        for entry in entries:
            all_tags.extend(entry.get('tags', []))
        
        analysis['tag_analysis'] = {
            'total_tags': len(all_tags),
            'unique_tags': len(set(all_tags)),
            'tag_frequency': dict(Counter(all_tags).most_common(10))
        }
        
        # Quality metrics
        analysis['quality_metrics'] = self._calculate_quality_metrics(entries)
        
        return analysis
    
    def _calculate_quality_metrics(self, entries: List[Dict]) -> Dict[str, Any]:
        """Calculate quality metrics for the parsed data"""
        metrics = {
            'completeness': 0.0,
            'consistency': 0.0,
            'issues': []
        }
        
        if not entries:
            return metrics
        
        # Check completeness
        required_fields = ['section_id', 'title', 'page', 'level']
        complete_entries = 0
        
        for entry in entries:
            if all(field in entry and entry[field] for field in required_fields):
                complete_entries += 1
        
        metrics['completeness'] = round(complete_entries / len(entries) * 100, 2)
        
        # Check consistency
        section_id_pattern = r'^\d+(\.\d+)*$'
        consistent_ids = 0
        
        for entry in entries:
            section_id = entry.get('section_id', '')
            if section_id and self._is_valid_section_id(section_id):
                consistent_ids += 1
        
        metrics['consistency'] = round(consistent_ids / len(entries) * 100, 2)
        
        # Identify issues
        issues = []
        
        # Check for missing parent references
        section_ids = {entry.get('section_id') for entry in entries}
        for entry in entries:
            parent_id = entry.get('parent_id')
            if parent_id and parent_id not in section_ids:
                issues.append(f"Missing parent '{parent_id}' for section '{entry.get('section_id')}'")
        
        # Check page order
        prev_page = 0
        for entry in entries:
            page = entry.get('page', 0)
            if page < prev_page:
                issues.append(f"Page order issue: section '{entry.get('section_id')}' on page {page}")
            prev_page = page
        
        metrics['issues'] = issues[:10]  # Limit to first 10 issues
        
        return metrics
    
    def _is_valid_section_id(self, section_id: str) -> bool:
        """Check if section ID follows expected pattern"""
        import re
        pattern = r'^\d+(\.\d+)*$'
        return bool(re.match(pattern, section_id))
    
    def generate_report(self, entries: List[Dict], output_file: str = None) -> str:
        """Generate comprehensive analysis report"""
        analysis = self.analyze_structure(entries)
        
        report = []
        report.append("USB PD SPECIFICATION TOC ANALYSIS REPORT")
        report.append("=" * 60)
        report.append("")
        
        # Basic statistics
        report.append("📊 BASIC STATISTICS")
        report.append(f"Total Entries: {analysis['total_entries']}")
        if analysis.get('hierarchy'):
            report.append(f"Maximum Depth: {analysis['hierarchy']['max_depth']}")
            report.append(f"Level Distribution: {analysis['hierarchy']['level_distribution']}")
        report.append("")
        
        # Page coverage
        if analysis.get('page_stats'):
            ps = analysis['page_stats']
            report.append("📄 PAGE COVERAGE")
            report.append(f"Page Range: {ps['first_page']} - {ps['last_page']}")
            report.append(f"Total Pages: {ps['total_pages_covered']}")
            report.append(f"Avg Pages/Section: {ps['avg_pages_per_section']}")
            report.append("")
        
        # Title analysis
        if analysis.get('title_analysis'):
            ta = analysis['title_analysis']
            report.append("📝 TITLE ANALYSIS")
            report.append(f"Average Title Length: {ta['avg_title_length']} characters")
            report.append(f"Longest Title: {ta['longest_title'][:80]}...")
            report.append(f"Shortest Title: {ta['shortest_title']}")
            report.append("")
        
        # Tag analysis
        if analysis.get('tag_analysis'):
            tag_a = analysis['tag_analysis']
            report.append("🏷️ TAG ANALYSIS")
            report.append(f"Total Tags: {tag_a['total_tags']}")
            report.append(f"Unique Tags: {tag_a['unique_tags']}")
            if tag_a['tag_frequency']:
                report.append("Most Common Tags:")
                for tag, count in list(tag_a['tag_frequency'].items())[:5]:
                    report.append(f"  - {tag}: {count}")
            report.append("")
        
        # Quality metrics
        if analysis.get('quality_metrics'):
            qm = analysis['quality_metrics']
            report.append("✅ QUALITY METRICS")
            report.append(f"Completeness: {qm['completeness']}%")
            report.append(f"Consistency: {qm['consistency']}%")
            
            if qm['issues']:
                report.append("⚠️ Issues Found:")
                for issue in qm['issues']:
                    report.append(f"  - {issue}")
            report.append("")
        
        # Sample entries
        report.append("📋 SAMPLE ENTRIES")
        for i, entry in enumerate(entries[:5]):
            report.append(f"{i+1}. {entry.get('full_path', 'N/A')} (Page {entry.get('page', 'N/A')})")
        
        if len(entries) > 5:
            report.append(f"... and {len(entries) - 5} more entries")
        
        report_text = "\n".join(report)
        
        # Save to file if requested
        if output_file:
            try:
                with open(output_file, 'w', encoding='utf-8') as f:
                    f.write(report_text)
                logger.info(f"Report saved to {output_file}")
            except Exception as e:
                logger.error(f"Error saving report: {e}")
        
        return report_text

class FileComparator:
    """Compare two JSONL files and highlight differences"""
    
    def __init__(self):
        self.analyzer = TOCAnalyzer()
    
    def compare_entries(self, entries1: List[Dict], entries2: List[Dict]) -> Dict[str, Any]:
        """Compare two sets of TOC entries"""
        comparison = {
            'file1_entries': len(entries1),
            'file2_entries': len(entries2),
            'common_sections': [],
            'unique_to_file1': [],
            'unique_to_file2': [],
            'structural_differences': {},
            'content_differences': []
        }
        
        # Get section IDs for comparison
        sections1 = {entry.get('section_id'): entry for entry in entries1}
        sections2 = {entry.get('section_id'): entry for entry in entries2}
        
        # Find common and unique sections
        ids1 = set(sections1.keys())
        ids2 = set(sections2.keys())
        
        comparison['common_sections'] = list(ids1.intersection(ids2))
        comparison['unique_to_file1'] = list(ids1 - ids2)
        comparison['unique_to_file2'] = list(ids2 - ids1)
        
        # Analyze structural differences
        analysis1 = self.analyzer.analyze_structure(entries1)
        analysis2 = self.analyzer.analyze_structure(entries2)
        
        comparison['structural_differences'] = {
            'hierarchy_diff': {
                'file1_max_depth': analysis1['hierarchy'].get('max_depth', 0),
                'file2_max_depth': analysis2['hierarchy'].get('max_depth', 0),
                'file1_levels': analysis1['hierarchy'].get('level_distribution', {}),
                'file2_levels': analysis2['hierarchy'].get('level_distribution', {})
            },
            'page_range_diff': {
                'file1_pages': analysis1.get('page_stats', {}),
                'file2_pages': analysis2.get('page_stats', {})
            }
        }
        
        # Check content differences for common sections
        for section_id in comparison['common_sections']:
            entry1 = sections1[section_id]
            entry2 = sections2[section_id]
            
            differences = []
            if entry1.get('title') != entry2.get('title'):
                differences.append(f"Title: '{entry1.get('title')}' vs '{entry2.get('title')}'")
            
            if entry1.get('page') != entry2.get('page'):
                differences.append(f"Page: {entry1.get('page')} vs {entry2.get('page')}")
            
            if entry1.get('level') != entry2.get('level'):
                differences.append(f"Level: {entry1.get('level')} vs {entry2.get('level')}")
            
            if differences:
                comparison['content_differences'].append({
                    'section_id': section_id,
                    'differences': differences
                })
        
        return comparison
    
    def generate_comparison_report(self, file1_path: str, file2_path: str, 
                                 comparison: Dict[str, Any]) -> str:
        """Generate a detailed comparison report"""
        report = []
        report.append("FILE COMPARISON REPORT")
        report.append("=" * 60)
        report.append(f"File 1: {file1_path}")
        report.append(f"File 2: {file2_path}")
        report.append("")
        
        # Summary statistics
        report.append("📊 SUMMARY")
        report.append(f"File 1 Entries: {comparison['file1_entries']}")
        report.append(f"File 2 Entries: {comparison['file2_entries']}")
        report.append(f"Common Sections: {len(comparison['common_sections'])}")
        report.append(f"Unique to File 1: {len(comparison['unique_to_file1'])}")
        report.append(f"Unique to File 2: {len(comparison['unique_to_file2'])}")
        report.append(f"Content Differences: {len(comparison['content_differences'])}")
        report.append("")
        
        # Structural differences
        struct_diff = comparison['structural_differences']
        report.append("🏗️ STRUCTURAL DIFFERENCES")
        
        hierarchy_diff = struct_diff['hierarchy_diff']
        report.append(f"Max Depth: {hierarchy_diff['file1_max_depth']} vs {hierarchy_diff['file2_max_depth']}")
        
        page_diff = struct_diff['page_range_diff']
        file1_pages = page_diff.get('file1_pages', {})
        file2_pages = page_diff.get('file2_pages', {})
        
        if file1_pages and file2_pages:
            report.append(f"Page Range File 1: {file1_pages.get('first_page', 'N/A')} - {file1_pages.get('last_page', 'N/A')}")
            report.append(f"Page Range File 2: {file2_pages.get('first_page', 'N/A')} - {file2_pages.get('last_page', 'N/A')}")
        report.append("")
        
        # Unique sections
        if comparison['unique_to_file1']:
            report.append("📋 SECTIONS UNIQUE TO FILE 1")
            for section_id in comparison['unique_to_file1'][:10]:
                report.append(f"  - {section_id}")
            if len(comparison['unique_to_file1']) > 10:
                report.append(f"  ... and {len(comparison['unique_to_file1']) - 10} more")
            report.append("")
        
        if comparison['unique_to_file2']:
            report.append("📋 SECTIONS UNIQUE TO FILE 2")
            for section_id in comparison['unique_to_file2'][:10]:
                report.append(f"  - {section_id}")
            if len(comparison['unique_to_file2']) > 10:
                report.append(f"  ... and {len(comparison['unique_to_file2']) - 10} more")
            report.append("")
        
        # Content differences
        if comparison['content_differences']:
            report.append("📝 CONTENT DIFFERENCES")
            for diff in comparison['content_differences'][:10]:
                report.append(f"Section {diff['section_id']}:")
                for difference in diff['differences']:
                    report.append(f"  - {difference}")
            
            if len(comparison['content_differences']) > 10:
                report.append(f"... and {len(comparison['content_differences']) - 10} more differences")
            report.append("")
        
        return "\n".join(report)

def demo_with_sample_pdf():
    """Demonstrate parsing with a sample PDF (if available)"""
    print("🔍 Looking for sample PDFs...")

    pdf_dir = Path("pdfs")
    sample_files = []

    if pdf_dir.exists():
        sample_files = list(pdf_dir.glob("*.pdf"))

    if not sample_files:
        print("❌ No PDF files found in 'pdfs/' directory")
        print("💡 To test the parser, add USB PD specification PDFs to the 'pdfs/' folder")
        return False

    print(f"📁 Found {len(sample_files)} PDF file(s):")
    for i, pdf_file in enumerate(sample_files):
        print(f"  {i+1}. {pdf_file.name}")

    # Let user select which PDF to process
    if len(sample_files) == 1:
        sample_pdf = sample_files[0]
        print(f"\n🚀 Processing the only PDF: {sample_pdf.name}")
    else:
        while True:
            try:
                choice = input(f"\nSelect PDF to process (1-{len(sample_files)}): ").strip()
                pdf_index = int(choice) - 1

                if 0 <= pdf_index < len(sample_files):
                    sample_pdf = sample_files[pdf_index]
                    print(f"\n🚀 Processing selected PDF: {sample_pdf.name}")
                    break
                else:
                    print(f"❌ Please enter a number between 1 and {len(sample_files)}")
            except ValueError:
                print("❌ Please enter a valid number")
            except KeyboardInterrupt:
                print("\n❌ Operation cancelled by user")
                return False
    
    try:
        # Initialize parser
        config = ParseConfig()
        parser = USBPDParser(config)
        
        # Create output directory
        output_dir = Path("output")
        output_dir.mkdir(exist_ok=True)
        
        # Parse the PDF
        output_file = output_dir / "usb_pd_spec.jsonl"
        entries, validation = parser.parse_pdf(str(sample_pdf), str(output_file))
        
        print(f"✅ Successfully parsed {len(entries)} TOC entries")
        print(f"💾 Results saved to: {output_file}")
        
        # Generate analysis report
        analyzer = TOCAnalyzer()
        entries_data = [entry.model_dump() for entry in entries]
        
        report_file = output_dir / "analysis_report.txt"
        report = analyzer.generate_report(entries_data, str(report_file))
        
        print(f"📊 Analysis report saved to: {report_file}")
        
        # Show summary
        print("\n" + "="*60)
        print("DEMO RESULTS SUMMARY")
        print("="*60)
        print(f"PDF Processed: {sample_pdf.name}")
        print(f"TOC Entries: {len(entries)}")
        print(f"Validation Issues: {len(validation.get('missing_parents', []))}")
        print(f"Output Files:")
        print(f"  - {output_file}")
        print(f"  - {report_file}")
        
        return True
        
    except Exception as e:
        logger.error(f"Demo failed: {e}")
        return False

def analyze_existing_file(jsonl_path: str):
    """Analyze an existing JSONL file"""
    print(f"📊 Analyzing existing file: {jsonl_path}")
    
    try:
        analyzer = TOCAnalyzer()
        entries = analyzer.load_jsonl(jsonl_path)
        
        report = analyzer.generate_report(entries)
        print("\n" + report)
        
        # Ask if user wants to save report
        save_report = input("\nSave report to file? (y/N): ").lower().strip()
        if save_report == 'y':
            report_file = Path(jsonl_path).parent / "analysis_report.txt"
            with open(report_file, 'w', encoding='utf-8') as f:
                f.write(report)
            print(f"📄 Report saved to: {report_file}")
            
    except Exception as e:
        logger.error(f"Analysis failed: {e}")

def compare_files(file1: str, file2: str):
    """Compare two JSONL files"""
    print(f"🔍 Comparing files:")
    print(f"  File 1: {file1}")
    print(f"  File 2: {file2}")
    
    try:
        comparator = FileComparator()
        analyzer = TOCAnalyzer()
        
        # Load both files
        entries1 = analyzer.load_jsonl(file1)
        entries2 = analyzer.load_jsonl(file2)
        
        # Perform comparison
        comparison = comparator.compare_entries(entries1, entries2)
        
        # Generate and display report
        report = comparator.generate_comparison_report(file1, file2, comparison)
        print("\n" + report)
        
        # Ask if user wants to save comparison report
        save_report = input("\nSave comparison report to file? (y/N): ").lower().strip()
        if save_report == 'y':
            report_file = Path("output") / "comparison_report.txt"
            Path("output").mkdir(exist_ok=True)
            
            with open(report_file, 'w', encoding='utf-8') as f:
                f.write(report)
            print(f"📄 Comparison report saved to: {report_file}")
            
    except Exception as e:
        logger.error(f"Comparison failed: {e}")

def interactive_demo():
    """Run an interactive demo session"""
    print("🎯 USB PD Parser Interactive Demo")
    print("=" * 40)
    print()

    while True:
        print("Available options:")
        print("1. Parse PDF files")
        print("2. Analyze existing JSONL file")
        print("3. Compare two JSONL files")
        print("4. Show sample data structure")
        print("5. List available PDFs")
        print("6. Exit")
        print()

        choice = input("Select an option (1-6): ").strip()
        
        if choice == '1':
            demo_with_sample_pdf()
        elif choice == '2':
            file_path = input("Enter JSONL file path: ").strip()
            if Path(file_path).exists():
                analyze_existing_file(file_path)
            else:
                print(f"❌ File not found: {file_path}")
        elif choice == '3':
            file1 = input("Enter first JSONL file path: ").strip()
            file2 = input("Enter second JSONL file path: ").strip()
            if Path(file1).exists() and Path(file2).exists():
                compare_files(file1, file2)
            else:
                print("❌ One or both files not found")
        elif choice == '4':
            show_sample_data_structure()
        elif choice == '5':
            list_available_pdfs()
        elif choice == '6':
            print("👋 Thanks for using USB PD Parser Demo!")
            break
        else:
            print("❌ Invalid option. Please select 1-6.")
        
        print("\n" + "-" * 40 + "\n")

def list_available_pdfs():
    """List all available PDF files in the pdfs directory"""
    print("📁 AVAILABLE PDF FILES")
    print("=" * 30)

    pdf_dir = Path("pdfs")
    if not pdf_dir.exists():
        print("❌ 'pdfs' directory not found")
        print("💡 Create a 'pdfs' folder and add your PDF files there")
        return

    pdf_files = list(pdf_dir.glob("*.pdf"))

    if not pdf_files:
        print("❌ No PDF files found in 'pdfs/' directory")
        print("💡 Add PDF files to the 'pdfs/' folder to process them")
        print(f"📂 PDF directory location: {pdf_dir.absolute()}")
    else:
        print(f"Found {len(pdf_files)} PDF file(s):")
        for i, pdf_file in enumerate(pdf_files, 1):
            file_size = pdf_file.stat().st_size
            size_mb = file_size / (1024 * 1024)
            print(f"  {i}. {pdf_file.name} ({size_mb:.1f} MB)")

        print(f"\n📂 PDF directory location: {pdf_dir.absolute()}")
        print("💡 Use option 1 to parse any of these PDFs")

def show_sample_data_structure():
    """Show sample data structure for reference"""
    sample_entry = {
        "doc_title": "USB Power Delivery Specification - Revision 3.1",
        "section_id": "2.1.2",
        "title": "Power Delivery Communication",
        "full_path": "2.1.2 Power Delivery Communication",
        "page": 45,
        "level": 3,
        "parent_id": "2.1",
        "tags": ["communication", "protocol"]
    }

    print("📋 SAMPLE DATA STRUCTURE")
    print("=" * 30)
    print(json.dumps(sample_entry, indent=2))
    print()
    print("Field Descriptions:")
    print("- doc_title: Document title or version")
    print("- section_id: Hierarchical section ID (e.g., '2.1.2')")
    print("- title: Section title without numbering")
    print("- full_path: Complete section path with ID and title")
    print("- page: Starting page number")
    print("- level: Hierarchy depth (1=chapter, 2=section, etc.)")
    print("- parent_id: Parent section ID")
    print("- tags: Semantic tags for categorization")

def main():
    """Main CLI interface"""
    parser = argparse.ArgumentParser(
        description="USB PD Parser Demo and Validation Script",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
    python demo.py                                    # Interactive demo
    python demo.py --analysis-only output/file.jsonl # Analyze existing file
    python demo.py --compare file1.jsonl file2.jsonl # Compare two files
        """
    )
    
    parser.add_argument('--analysis-only', metavar='FILE',
                       help='Analyze an existing JSONL file')
    parser.add_argument('--compare', nargs=2, metavar=('FILE1', 'FILE2'),
                       help='Compare two JSONL files')
    parser.add_argument('--sample-data', action='store_true',
                       help='Show sample data structure')
    parser.add_argument('--verbose', '-v', action='store_true',
                       help='Enable verbose logging')
    
    args = parser.parse_args()
    
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    
    # Handle different modes
    if args.analysis_only:
        analyze_existing_file(args.analysis_only)
    elif args.compare:
        compare_files(args.compare[0], args.compare[1])
    elif args.sample_data:
        show_sample_data_structure()
    else:
        # No arguments provided, run interactive demo
        interactive_demo()

if __name__ == "__main__":
    main()