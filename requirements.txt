# Core PDF processing libraries
PyPDF2>=3.0.1
pdfplumber>=0.9.0
pymupdf>=1.23.0

# Data processing and analysis
pandas>=2.0.0
numpy>=1.24.0
openpyxl>=3.1.0

# JSON and data serialization
jsonlines>=3.1.0
jsonschema>=4.17.0

# Text processing and NLP
nltk>=3.8
textract>=1.6.5
python-docx>=0.8.11

# Machine learning and pattern recognition
scikit-learn>=1.3.0
spacy>=3.6.0

# Web scraping and additional data sources
requests>=2.31.0
beautifulsoup4>=4.12.0

# Logging and configuration
colorlog>=6.7.0
python-dotenv>=1.0.0

# Testing and development
pytest>=7.4.0
pytest-cov>=4.1.0
black>=23.0.0
flake8>=6.0.0

# Optional: For advanced PDF processing
tabula-py>=2.7.0
camelot-py[cv]>=0.11.0

# Optional: For machine learning enhanced extraction
transformers>=4.30.0
torch>=2.0.0

# Utilities
tqdm>=4.65.0
click>=8.1.0
pathlib2>=2.3.7
pyyaml>=6.0