# USB PD Parser Configuration File

# Input/Output Settings
directories:
  input_pdf: "pdfs"
  output_json: "output"
  logs: "logs"
  temp: "temp"
  exports: "output/exports"

# Parser Settings
parser:
  # PDF processing options
  pdf_engine: "pdfplumber"  # Options: pdfplumber, pypdf2, pymupdf
  fallback_engine: "pypdf2"
  extract_images: false
  extract_tables: true
  extract_metadata: true
  
  # Text processing
  clean_text: true
  remove_headers_footers: true
  merge_hyphenated_words: true
  preserve_formatting: false
  
  # Content extraction patterns
  extract_voltage_specs: true
  extract_current_specs: true
  extract_power_specs: true
  extract_message_types: true
  extract_data_objects: true
  extract_test_conditions: true
  extract_compliance_requirements: true

# Output Formats
output:
  formats:
    - json
    - jsonl
  
  # JSON output settings
  json:
    pretty_print: true
    indent: 2
    ensure_ascii: false
    sort_keys: true
  
  # JSONL output settings
  jsonl:
    separate_by_type: true
    include_metadata: true
    compress: false

# Processing Rules
processing:
  # Section detection
  section_patterns:
    - '^(\d+(?:\.\d+)*)\s+([A-Z][A-Za-z\s]+)$'
    - '^([A-Z][A-Za-z\s]{3,50})$'
  
  # Table detection
  table_patterns:
    - 'Table\s+(\d+(?:\.\d+)*)[:\-\s]+(.+)$'
    - 'TABLE\s+(\d+(?:\.\d+)*)[:\-\s]+(.+)$'
  
  # Figure detection  
  figure_patterns:
    - 'Figure\s+(\d+(?:\.\d+)*)[:\-\s]+(.+)$'
    - 'FIGURE\s+(\d+(?:\.\d+)*)[:\-\s]+(.+)$'
  
  # Specification patterns
  voltage_patterns:
    - '(\d+(?:\.\d+)?)\s*V(?:DC)?'
    - '(\d+(?:\.\d+)?)\s*[Vv]olt'
  
  current_patterns:
    - '(\d+(?:\.\d+)?)\s*A(?:DC)?'
    - '(\d+(?:\.\d+)?)\s*[Aa]mp'
  
  power_patterns:
    - '(\d+(?:\.\d+)?)\s*W'
    - '(\d+(?:\.\d+)?)\s*[Ww]att'
  
  # Message and protocol patterns
  message_patterns:
    - '([A-Z_]+)\s+Message'
    - '([A-Z_]+)\s+MSG'
  
  data_object_patterns:
    - '([A-Z_]+)\s+Data\s+Object'
    - '([A-Z_]+)\s+DO'
  
  # Compliance patterns
  compliance_patterns:
    - '(?:SHALL|MUST|REQUIRED)[:\s]+(.+)'
    - '(?:SHOULD|RECOMMENDED)[:\s]+(.+)'
    - '(?:MAY|OPTIONAL)[:\s]+(.+)'

# Validation Rules
validation:
  required_sections:
    - "introduction"
    - "overview"
    - "specification"
    - "protocol"
  
  expected_voltage_ranges:
    min: 3.0
    max: 48.0
  
  expected_current_ranges:
    min: 0.1
    max: 10.0
  
  expected_power_ranges:
    min: 1.0
    max: 240.0

# Logging Configuration
logging:
  level: "INFO"  # DEBUG, INFO, WARNING, ERROR, CRITICAL
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  file_logging: true
  console_logging: true
  max_file_size: "10MB"
  backup_count: 5

# Performance Settings
performance:
  multiprocessing: true
  max_workers: 4
  chunk_size: 1000
  memory_limit: "1GB"
  timeout: 300  # seconds

# Feature Flags
features:
  enable_ml_extraction: false
  enable_ocr: false
  enable_table_structure_detection: true
  enable_semantic_analysis: false
  enable_cross_reference_linking: true

# Export Settings
export:
  excel:
    enabled: true
    separate_sheets: true
    include_charts: false
  
  csv:
    enabled: true
    delimiter: ","
    encoding: "utf-8"
  
  html:
    enabled: false
    include_styling: true
    responsive: true

# Quality Assurance
quality:
  min_text_extraction_ratio: 0.8  # Minimum ratio of successfully extracted text
  max_processing_time: 600  # Maximum time per PDF in seconds
  enable_validation: true
  enable_checksums: true