#!/usr/bin/env python3
"""
Test Cases for USB PD Parser
Comprehensive testing of all parser components
"""

import unittest
import json
import os
import sys
from pathlib import Path
import tempfile
import shutil
from unittest.mock import patch, MagicMock

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Import your modules
try:
    import usb_pd_parser
    from validate import OutputValidator
    import demo
except ImportError as e:
    print(f"Import Error: {e}")
    print("Make sure all required files are in the same directory")
    sys.exit(1)

class TestUSBPDParser(unittest.TestCase):
    """Test cases for USB PD Parser"""
    
    def setUp(self):
        """Set up test environment"""
        self.test_dir = tempfile.mkdtemp()
        self.pdfs_dir = Path(self.test_dir) / "pdfs"
        self.output_dir = Path(self.test_dir) / "output"
        
        # Create test directories
        self.pdfs_dir.mkdir(exist_ok=True)
        self.output_dir.mkdir(exist_ok=True)
        
        # Create a sample test PDF (mock content)
        self.test_pdf = self.pdfs_dir / "test_spec.pdf"
        self.test_pdf.touch()  # Create empty file for testing
        
        print(f"Test setup complete: {self.test_dir}")
    
    def tearDown(self):
        """Clean up test environment"""
        shutil.rmtree(self.test_dir, ignore_errors=True)
    
    def test_1_imports(self):
        """Test 1: Check if all modules import correctly"""
        print("\n=== Test 1: Module Imports ===")
        
        try:
            import usb_pd_parser
            print("✅ usb_pd_parser imported successfully")
        except ImportError:
            self.fail("❌ Failed to import usb_pd_parser")
        
        try:
            from validate import OutputValidator
            print("✅ OutputValidator imported successfully")
        except ImportError:
            self.fail("❌ Failed to import OutputValidator")
        
        try:
            import demo
            print("✅ demo module imported successfully")
        except ImportError:
            self.fail("❌ Failed to import demo")
    
    def test_2_config_file(self):
        """Test 2: Check if config.yaml exists and is valid"""
        print("\n=== Test 2: Configuration File ===")
        
        config_file = Path("config.yaml")
        self.assertTrue(config_file.exists(), "❌ config.yaml not found")
        print("✅ config.yaml exists")
        
        # Try to load config
        import yaml
        try:
            with open(config_file, 'r') as f:
                config = yaml.safe_load(f)
            print("✅ config.yaml is valid YAML")
            print(f"   Config keys: {list(config.keys())}")
        except Exception as e:
            self.fail(f"❌ config.yaml is invalid: {e}")
    
    def test_3_requirements_file(self):
        """Test 3: Check requirements.txt"""
        print("\n=== Test 3: Requirements File ===")
        
        req_file = Path("requirements.txt")
        self.assertTrue(req_file.exists(), "❌ requirements.txt not found")
        print("✅ requirements.txt exists")
        
        with open(req_file, 'r') as f:
            requirements = f.read().strip().split('\n')
        
        expected_packages = ['PyMuPDF', 'pdfplumber', 'pandas', 'numpy', 'pyyaml']
        for pkg in expected_packages:
            found = any(pkg.lower() in req.lower() for req in requirements)
            if found:
                print(f"✅ {pkg} found in requirements")
            else:
                print(f"⚠️  {pkg} not found in requirements")
    
    def test_4_directory_structure(self):
        """Test 4: Check directory structure"""
        print("\n=== Test 4: Directory Structure ===")
        
        required_files = [
            "usb_pd_parser.py", "demo.py", "validate.py", 
            "config.yaml", "requirements.txt", ".gitignore"
        ]
        
        for file_name in required_files:
            file_path = Path(file_name)
            if file_path.exists():
                print(f"✅ {file_name} exists")
            else:
                print(f"❌ {file_name} missing")
                self.fail(f"Required file missing: {file_name}")
        
        # Check if directories can be created
        test_dirs = ["pdfs", "output", "output/exports"]
        for dir_name in test_dirs:
            dir_path = Path(dir_name)
            if not dir_path.exists():
                try:
                    dir_path.mkdir(parents=True, exist_ok=True)
                    print(f"✅ Created directory: {dir_name}")
                except Exception as e:
                    print(f"⚠️  Could not create {dir_name}: {e}")
            else:
                print(f"✅ Directory exists: {dir_name}")
    
    def test_5_parser_initialization(self):
        """Test 5: Test parser initialization"""
        print("\n=== Test 5: Parser Initialization ===")
        
        try:
            # Test if parser has required classes/functions
            if hasattr(usb_pd_parser, 'USBPDParser'):
                parser = usb_pd_parser.USBPDParser()
                print("✅ USBPDParser class initialized")
            else:
                print("⚠️  USBPDParser class not found, checking for functions...")
                
            # Check for required methods/functions
            expected_functions = ['parse_pdf', 'extract_text', 'parse_sections']
            for func_name in expected_functions:
                if hasattr(usb_pd_parser, func_name):
                    print(f"✅ Function {func_name} found")
                else:
                    print(f"⚠️  Function {func_name} not found")
        
        except Exception as e:
            print(f"⚠️  Parser initialization issue: {e}")
    
    def test_6_validator_initialization(self):
        """Test 6: Test validator initialization"""
        print("\n=== Test 6: Validator Initialization ===")
        
        try:
            validator = OutputValidator()
            print("✅ OutputValidator initialized successfully")
            
            # Test validation methods
            test_data = {
                "version": "1.0",
                "title": "Test Document",
                "sections": [],
                "tables": [],
                "voltage_profiles": [],
                "current_profiles": [],
                "metadata": {"test": "data"}
            }
            
            errors, warnings = validator.validate_json_structure(test_data)
            print(f"✅ Validation methods work (errors: {len(errors)}, warnings: {len(warnings)})")
            
        except Exception as e:
            self.fail(f"❌ Validator initialization failed: {e}")
    
    def test_7_sample_data_validation(self):
        """Test 7: Test with sample data"""
        print("\n=== Test 7: Sample Data Validation ===")
        
        # Create sample valid data
        sample_data = {
            "version": "3.1",
            "title": "USB Power Delivery Specification",
            "sections": [
                {
                    "number": "1.1",
                    "title": "Introduction",
                    "content": "This section introduces USB PD..."
                }
            ],
            "tables": [
                {
                    "number": "1",
                    "title": "Voltage Profiles",
                    "headers": ["Profile", "Voltage", "Current"],
                    "rows": [["Profile 1", "5V", "3A"]]
                }
            ],
            "voltage_profiles": [
                {"voltage": 5.0, "profile": "Standard"},
                {"voltage": 9.0, "profile": "Fast Charge"},
                {"voltage": 20.0, "profile": "High Power"}
            ],
            "current_profiles": [
                {"current": 1.5, "profile": "Low Power"},
                {"current": 3.0, "profile": "Standard"}
            ],
            "metadata": {
                "extraction_date": "2025-01-20",
                "file_name": "test.pdf",
                "total_pages": 100
            }
        }
        
        # Test validation
        validator = OutputValidator()
        errors, warnings = validator._validate_single_object(sample_data)
        
        print(f"✅ Sample data validation complete")
        print(f"   Errors: {len(errors)}")
        print(f"   Warnings: {len(warnings)}")
        
        if errors:
            print("   Error details:")
            for error in errors[:3]:  # Show first 3 errors
                print(f"     - {error}")
        
        # Calculate completeness score
        score = validator.calculate_completeness_score(sample_data)
        print(f"✅ Completeness score: {score:.1f}/100")
        
        self.assertGreaterEqual(score, 50, "Sample data should have reasonable completeness score")
    
    def test_8_file_operations(self):
        """Test 8: Test file operations"""
        print("\n=== Test 8: File Operations ===")
        
        # Test JSON file creation and validation
        test_file = self.output_dir / "test_output.json"
        
        sample_data = {
            "version": "1.0",
            "title": "Test Output",
            "sections": [],
            "tables": [],
            "voltage_profiles": [],
            "current_profiles": [],
            "metadata": {"test": True}
        }
        
        # Write test file
        with open(test_file, 'w') as f:
            json.dump(sample_data, f, indent=2)
        
        print(f"✅ Test JSON file created: {test_file}")
        
        # Validate the file
        validator = OutputValidator()
        result = validator.validate_file(str(test_file))
        
        print(f"✅ File validation complete")
        print(f"   Valid: {result.is_valid}")
        print(f"   Score: {result.score:.1f}")
        print(f"   Errors: {len(result.errors)}")
        print(f"   Warnings: {len(result.warnings)}")
    
    def test_9_demo_functionality(self):
        """Test 9: Test demo script functionality"""
        print("\n=== Test 9: Demo Script ===")
        
        try:
            # Check if demo has main functions
            if hasattr(demo, 'main'):
                print("✅ demo.main() function found")
            
            if hasattr(demo, 'run_demo'):
                print("✅ demo.run_demo() function found")
            
            # Test demo initialization
            print("✅ Demo script can be imported and accessed")
            
        except Exception as e:
            print(f"⚠️  Demo script issue: {e}")
    
    def test_10_integration_test(self):
        """Test 10: Integration test"""
        print("\n=== Test 10: Integration Test ===")
        
        try:
            # This would be a full integration test
            # For now, just check if all components can work together
            
            validator = OutputValidator()
            print("✅ Validator ready")
            
            # Check if output directory is writable
            test_output = self.output_dir / "integration_test.json"
            test_output.write_text('{"test": "integration"}')
            
            if test_output.exists():
                print("✅ Output directory is writable")
                test_output.unlink()  # Clean up
            
            print("✅ Integration test framework ready")
            
        except Exception as e:
            print(f"⚠️  Integration test setup issue: {e}")


def run_manual_tests():
    """Run additional manual tests"""
    print("\n" + "="*60)
    print("MANUAL VERIFICATION CHECKLIST")
    print("="*60)
    
    checklist = [
        "1. Are all Python files syntactically correct?",
        "2. Can you import all modules without errors?",
        "3. Do you have PDF files in the pdfs/ directory?",
        "4. Can you run 'python demo.py' without errors?",
        "5. Can you run 'python validate.py --help'?",
        "6. Are all required packages installed?",
        "7. Does the output/ directory get created?",
        "8. Can you process a sample PDF file?"
    ]
    
    for item in checklist:
        print(f"☐ {item}")
    
    print("\n" + "="*60)
    print("QUICK COMMAND TESTS")
    print("="*60)
    
    commands = [
        "python -c 'import usb_pd_parser; print(\"Parser import OK\")'",
        "python -c 'from validate import OutputValidator; print(\"Validator import OK\")'",
        "python -c 'import demo; print(\"Demo import OK\")'",
        "python validate.py --help",
        "python demo.py --help"
    ]
    
    for cmd in commands:
        print(f"$ {cmd}")
    
    print(f"\n{'='*60}")


if __name__ == "__main__":
    print("USB PD PARSER - COMPREHENSIVE TEST SUITE")
    print("="*60)
    
    # Run unit tests
    unittest.main(argv=[''], exit=False, verbosity=2)
    
    # Run manual tests
    run_manual_tests()