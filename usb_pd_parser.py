#!/usr/bin/env python3
"""
USB Power Delivery Specification Parser

This script extracts Table of Contents from USB PD specification PDF files
and converts them into structured JSONL format for machine processing.

Author: AI Assistant
Created: 2025-07-20

Usage:
    python usb_pd_parser.py <pdf_file_path> [options]

Dependencies:
    pip install pdfplumber PyMuPDF pydantic
"""

import json
import re
import sys
import os
import argparse
from pathlib import Path
from typing import List, Dict, Optional, Tuple
from dataclasses import dataclass
import logging

try:
    import pdfplumber
    import fitz  # PyMuPDF
    from pydantic import BaseModel, Field
except ImportError as e:
    print(f"Missing required dependency: {e}")
    print("Please install: pip install pdfplumber PyMuPDF pydantic")
    sys.exit(1)

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class TOCEntry(BaseModel):
    """Pydantic model for Table of Contents entry"""
    doc_title: str = Field(description="Document title or version")
    section_id: str = Field(description="Hierarchical section ID (e.g., '2.1.2')")
    title: str = Field(description="Section title without numbering")
    full_path: str = Field(description="Complete section path with ID and title")
    page: int = Field(description="Starting page number")
    level: int = Field(description="Hierarchy depth (1=chapter, 2=section, etc.)")
    parent_id: Optional[str] = Field(description="Parent section ID", default=None)
    tags: List[str] = Field(description="Semantic tags for categorization", default=[])

@dataclass
class ParseConfig:
    """Configuration for PDF parsing"""
    toc_search_pages: int = 15  # Number of pages to search for TOC
    min_toc_entries: int = 5    # Minimum TOC entries to consider valid
    max_toc_entries: int = 500  # Maximum TOC entries to prevent runaway parsing

class USBPDParser:
    """USB Power Delivery Specification Parser"""
    
    def __init__(self, config: ParseConfig = None):
        self.config = config or ParseConfig()
        self.toc_patterns = [
            # Pattern 1: "2.1.2 Title Name .............. 45"
            r'^(\d+(?:\.\d+)*)\s+([^.]+?)[\s.]{3,}\s*(\d+)\s*$',
            # Pattern 2: "2.1.2 Title Name 45"
            r'^(\d+(?:\.\d+)*)\s+(.+?)\s+(\d+)\s*$',
            # Pattern 3: More flexible with optional dots
            r'^(\d+(?:\.\d+)*)\s+(.+?)(?:[\s.]{2,})\s*(\d+)\s*$'
        ]
        
    def extract_document_title(self, pdf_path: str) -> str:
        """Extract document title from PDF metadata or filename"""
        try:
            with fitz.open(pdf_path) as doc:
                metadata = doc.metadata
                if metadata.get('title'):
                    return metadata['title']
                if metadata.get('subject'):
                    return metadata['subject']
        except Exception as e:
            logger.warning(f"Could not extract metadata: {e}")
        
        # Fallback to filename
        filename = Path(pdf_path).stem
        return f"USB Power Delivery Specification - {filename}"
    
    def extract_text_from_pages(self, pdf_path: str, start_page: int = 0, end_page: int = None) -> List[str]:
        """Extract text from PDF pages using pdfplumber"""
        pages_text = []
        
        try:
            with pdfplumber.open(pdf_path) as pdf:
                total_pages = len(pdf.pages)
                end_page = min(end_page or self.config.toc_search_pages, total_pages)
                
                for page_num in range(start_page, end_page):
                    if page_num < total_pages:
                        page = pdf.pages[page_num]
                        text = page.extract_text() or ""
                        pages_text.append(text)
                        logger.debug(f"Extracted text from page {page_num + 1}")
                    
        except Exception as e:
            logger.error(f"Error extracting text from {pdf_path}: {e}")
            raise
            
        return pages_text
    
    def find_toc_section(self, pages_text: List[str]) -> Tuple[int, List[str]]:
        """Find and extract Table of Contents section from pages"""
        toc_indicators = [
            r'table\s+of\s+contents',
            r'contents',
            r'index',
            r'^contents$'
        ]
        
        for page_idx, page_text in enumerate(pages_text):
            lines = page_text.split('\n')
            
            # Look for TOC indicators
            for line_idx, line in enumerate(lines):
                line_lower = line.strip().lower()
                
                for indicator in toc_indicators:
                    if re.search(indicator, line_lower):
                        logger.info(f"Found TOC indicator '{line.strip()}' on page {page_idx + 1}")
                        
                        # Extract TOC lines (usually start after the header)
                        toc_lines = []
                        for toc_line in lines[line_idx + 1:]:
                            if toc_line.strip():
                                toc_lines.append(toc_line)
                        
                        # Also check subsequent pages for continuation
                        for next_page_idx in range(page_idx + 1, len(pages_text)):
                            next_lines = pages_text[next_page_idx].split('\n')
                            for next_line in next_lines:
                                if next_line.strip() and self._looks_like_toc_entry(next_line):
                                    toc_lines.append(next_line)
                                elif next_line.strip() and not self._looks_like_toc_entry(next_line):
                                    # Stop if we hit non-TOC content
                                    break
                        
                        if len(toc_lines) >= self.config.min_toc_entries:
                            return page_idx, toc_lines
        
        raise ValueError("Table of Contents not found in the specified pages")
    
    def _looks_like_toc_entry(self, line: str) -> bool:
        """Quick check if a line looks like a TOC entry"""
        for pattern in self.toc_patterns:
            if re.match(pattern, line.strip()):
                return True
        return False
    
    def parse_toc_entries(self, toc_lines: List[str], doc_title: str) -> List[TOCEntry]:
        """Parse TOC lines into structured entries"""
        entries = []
        
        for line in toc_lines:
            line = line.strip()
            if not line:
                continue
                
            # Try each pattern
            match = None
            for pattern in self.toc_patterns:
                match = re.match(pattern, line)
                if match:
                    break
            
            if not match:
                logger.debug(f"Could not parse TOC line: {line}")
                continue
            
            try:
                section_id = match.group(1).strip()
                title = match.group(2).strip()
                page = int(match.group(3).strip())
                
                # Clean up title (remove extra dots, spaces)
                title = re.sub(r'[\s.]{2,}', ' ', title).strip()
                
                # Calculate hierarchy level and parent
                level = len(section_id.split('.'))
                parent_id = self._get_parent_id(section_id)
                
                # Generate semantic tags
                tags = self._generate_tags(title)
                
                # Create full path
                full_path = f"{section_id} {title}"
                
                entry = TOCEntry(
                    doc_title=doc_title,
                    section_id=section_id,
                    title=title,
                    full_path=full_path,
                    page=page,
                    level=level,
                    parent_id=parent_id,
                    tags=tags
                )
                
                entries.append(entry)
                logger.debug(f"Parsed: {entry.full_path} (Page {entry.page})")
                
            except (ValueError, IndexError) as e:
                logger.warning(f"Error parsing line '{line}': {e}")
                continue
        
        logger.info(f"Successfully parsed {len(entries)} TOC entries")
        return entries
    
    def _get_parent_id(self, section_id: str) -> Optional[str]:
        """Get parent section ID from current section ID"""
        parts = section_id.split('.')
        if len(parts) <= 1:
            return None
        return '.'.join(parts[:-1])
    
    def _generate_tags(self, title: str) -> List[str]:
        """Generate semantic tags based on title content"""
        tags = []
        title_lower = title.lower()
        
        # Define keyword mappings
        tag_keywords = {
            'negotiation': ['negotiation', 'negotiate'],
            'contracts': ['contract', 'contracts'],
            'communication': ['communication', 'message', 'messages'],
            'devices': ['device', 'devices', 'sink', 'source'],
            'protocol': ['protocol', 'state', 'machine'],
            'power': ['power', 'voltage', 'current'],
            'cable': ['cable', 'plug', 'connector'],
            'revision': ['revision', 'compatibility', 'version'],
            'testing': ['test', 'testing', 'compliance'],
            'safety': ['safety', 'protection', 'fault'],
            'applications': ['application', 'use', 'uses']
        }
        
        for tag, keywords in tag_keywords.items():
            if any(keyword in title_lower for keyword in keywords):
                tags.append(tag)
        
        return tags
    
    def validate_toc_structure(self, entries: List[TOCEntry]) -> Dict:
        """Validate the parsed TOC structure"""
        validation_results = {
            'total_entries': len(entries),
            'levels': {},
            'missing_parents': [],
            'page_order_issues': [],
            'duplicate_sections': []
        }
        
        # Count entries by level
        for entry in entries:
            level = entry.level
            validation_results['levels'][level] = validation_results['levels'].get(level, 0) + 1
        
        # Check for missing parents
        section_ids = {entry.section_id for entry in entries}
        for entry in entries:
            if entry.parent_id and entry.parent_id not in section_ids:
                validation_results['missing_parents'].append(entry.section_id)
        
        # Check page order
        prev_page = 0
        for entry in entries:
            if entry.page < prev_page:
                validation_results['page_order_issues'].append(entry.section_id)
            prev_page = entry.page
        
        # Check for duplicates
        seen_sections = set()
        for entry in entries:
            if entry.section_id in seen_sections:
                validation_results['duplicate_sections'].append(entry.section_id)
            seen_sections.add(entry.section_id)
        
        return validation_results
    
    def save_to_jsonl(self, entries: List[TOCEntry], output_path: str) -> None:
        """Save TOC entries to JSONL file"""
        output_path = Path(output_path)
        output_path.parent.mkdir(parents=True, exist_ok=True)
        
        try:
            with open(output_path, 'w', encoding='utf-8') as f:
                for entry in entries:
                    json_line = entry.model_dump_json()
                    f.write(json_line + '\n')
            
            logger.info(f"Successfully saved {len(entries)} entries to {output_path}")
            
        except Exception as e:
            logger.error(f"Error saving to {output_path}: {e}")
            raise
    
    def parse_pdf(self, pdf_path: str, output_path: str = None) -> Tuple[List[TOCEntry], Dict]:
        """Main method to parse PDF and extract TOC"""
        pdf_path = Path(pdf_path)
        if not pdf_path.exists():
            raise FileNotFoundError(f"PDF file not found: {pdf_path}")
        
        logger.info(f"Starting to parse PDF: {pdf_path}")
        
        # Extract document title
        doc_title = self.extract_document_title(str(pdf_path))
        logger.info(f"Document title: {doc_title}")
        
        # Extract text from initial pages
        pages_text = self.extract_text_from_pages(str(pdf_path))
        
        # Find and extract TOC
        toc_page_idx, toc_lines = self.find_toc_section(pages_text)
        logger.info(f"Found TOC on page {toc_page_idx + 1} with {len(toc_lines)} lines")
        
        # Parse TOC entries
        entries = self.parse_toc_entries(toc_lines, doc_title)
        
        if len(entries) < self.config.min_toc_entries:
            raise ValueError(f"Too few TOC entries found: {len(entries)} < {self.config.min_toc_entries}")
        
        # Validate structure
        validation_results = self.validate_toc_structure(entries)
        
        # Save to file if output path provided
        if output_path:
            self.save_to_jsonl(entries, output_path)
        
        return entries, validation_results

def main():
    """Main CLI interface"""
    parser = argparse.ArgumentParser(
        description="Parse USB PD specification PDFs and extract Table of Contents",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
    python usb_pd_parser.py pdfs/usb_pd_spec_rev3.1.pdf
    python usb_pd_parser.py pdfs/spec.pdf -o output/custom_output.jsonl
    python usb_pd_parser.py pdfs/spec.pdf --validate-only
        """
    )
    
    parser.add_argument('pdf_path', help='Path to USB PD specification PDF file')
    parser.add_argument('-o', '--output', 
                       help='Output JSONL file path (default: usb_pd_spec.jsonl)')
    parser.add_argument('--validate-only', action='store_true',
                       help='Only validate structure, do not save output')
    parser.add_argument('--verbose', '-v', action='store_true',
                       help='Enable verbose logging')
    parser.add_argument('--search-pages', type=int, default=15,
                       help='Number of pages to search for TOC (default: 15)')
    
    args = parser.parse_args()
    
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    
    # Setup configuration
    config = ParseConfig()
    config.toc_search_pages = args.search_pages
    
    # Initialize parser
    pdf_parser = USBPDParser(config)
    
    try:
        # Determine output path
        if not args.validate_only:
            if args.output:
                output_path = args.output
            else:
                output_path = 'usb_pd_spec.jsonl'
        else:
            output_path = None
        
        # Parse PDF
        entries, validation = pdf_parser.parse_pdf(args.pdf_path, output_path)
        
        # Print summary
        print(f"\n{'='*60}")
        print(f"PARSING SUMMARY")
        print(f"{'='*60}")
        print(f"PDF File: {args.pdf_path}")
        print(f"Total TOC Entries: {validation['total_entries']}")
        print(f"Hierarchy Levels: {dict(validation['levels'])}")
        
        if validation['missing_parents']:
            print(f"⚠️  Missing Parents: {validation['missing_parents']}")
        
        if validation['page_order_issues']:
            print(f"⚠️  Page Order Issues: {validation['page_order_issues']}")
        
        if validation['duplicate_sections']:
            print(f"⚠️  Duplicate Sections: {validation['duplicate_sections']}")
        
        if not args.validate_only:
            print(f"✅ Output saved to: {output_path}")
        
        print(f"{'='*60}")
        
    except Exception as e:
        logger.error(f"Error processing PDF: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()